<template>
  <div
    ref="selectOptionElRef"
    class="select-option w-100"
    :class="{
      'select-option-open': isOpen,
      'select-option-has-confirm': props.hasConfirm,
      'select-option-has-search': props.hasSearch,
    }"
    role="combobox"
    :aria-expanded="isOpen"
    :aria-label="placeholder"
    :aria-controls="dropdownId"
    :aria-activedescendant="focusedOptionId"
    aria-haspopup="listbox"
  >
    <div
      class="select-option-container w-100 cursor-pointer"
      @click.stop="openDropdown"
    >
      <button
        type="button"
        class="btn-reset select-option-btn-icon"
        @click.stop="toggleDropdown"
        :aria-label="isOpen ? 'Close dropdown' : 'Open dropdown'"
      >
        <img src="@/assets/images/arrow-right.png" class="select-option-icon" alt="arrow icon" aria-hidden="true" />
      </button>

      <input
        type="text"
        class="w-100 font-size-base font-normal font-family-averta color-black-mine-shaft"
        :class="{
          'cursor-pointer': !inputModel && !isOpen
        }"
        v-model.trim="inputModel"
        @input="handleSearch"
        :readonly="isInputReadonly"
        :placeholder="placeholder"
        :aria-expanded="isOpen"
        :aria-controls="dropdownId"
        :aria-activedescendant="focusedOptionId"
        aria-autocomplete="list"
        role="combobox"
        :aria-label="placeholder"
      />
    </div>

    <Teleport to="body">
      <div
        v-if="isOpen"
        :id="dropdownId"
        ref="dropdownRef"
        :style="dropdownStyles"
        class="select-option-dropdown"
        role="listbox"
        aria-label="Select options list"
        v-on-click-outside="onClickOutsideHandler"
        :data-dropdown-position="dropdownPosition"
        tabindex="-1"
      >
        <div v-if="hasSearch && isInputReadonly" class="select-option-dropdown-search">
          <input
            ref="searchInputRef"
            type="text"
            class="w-100 font-size-base font-normal font-family-averta color-black-mine-shaft"
            @input="handleSearch"
            :placeholder="placeholder"
            aria-label="Search options"
            :aria-controls="dropdownId"
            role="searchbox"
            autocomplete="off"
          >
        </div>
        <div class="select-option-dropdown-list">
          <button
            v-if="hasAddOption"
            type="button"
            class="btn-reset select-option-option select-option-option-add w-100"
            :class="{ 'select-option-option-focused': focusedOptionId === 'add-option' }"
            @click.stop="addNewOption"
            role="option"
            :aria-selected="false"
            :aria-label="addOptionLabel"
            :data-option-uuid="'add-option'"
            :id="`option-add-option`"
          >
            <span class="select-option-option-add-icon font-size-28 font-normal color-slate-gray">+</span>
            <span class="font-size-base font-normal color-black-mine-shaft">{{ addOptionLabel }}</span>
          </button>
          <div
            v-for="option in filteredOptions"
            :key="option.uuid"
            class="select-option-option"
            :class="{
              'select-option-option-selected': radioModel?.uuid === option.uuid,
              'select-option-option-focused': focusedOptionId === option.uuid
            }"
            role="option"
            :aria-selected="radioModel?.uuid === option.uuid"
            :aria-label="option.name"
            :data-option-uuid="option.uuid"
            :id="`option-${option.uuid}`"
            @click="radioModel = option"
          >
            <label class="control-radio w-100">
              <span class="font-size-base font-normal color-black-mine-shaft">{{ option.name }}</span>
              <input
                type="radio"
                name="dropdown-option"
                :value="option"
                v-model="radioModel"
                tabindex="-1"
              >
              <span class="checkmark"></span>
            </label>
          </div>
        </div>
        <div v-if="hasConfirm" class="select-option-dropdown-confirm">
          <button
            type="button"
            class="btn-green btn-small"
            @click.stop="selectOption"
            :disabled="!radioModel || radioModel.uuid === model"
          >{{ $t('BUTTONS.APPLY_BUTTON') }}</button>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, reactive, nextTick, watch, onMounted } from 'vue';
import { vOnClickOutside } from '@vueuse/components';
import { useEventListener } from '@vueuse/core';

const DROPDOWN_POSITIONS = {
  TOP: 'top',
  BOTTOM: 'bottom'
};

const MAX_DROPDOWN_HEIGHT = 380;

const props = defineProps({
  options: {
    type: Array,
    default: [], // [{uuid: "", name: ""}]
  },
  placeholder: {
    type: String,
    default: 'Select option',
  },
  hasConfirm: {
    type: Boolean,
    default: true,
  },
  hasSearch: {
    type: Boolean,
    default: true,
  },
  hasAddOption: {
    type: Boolean,
    default: true,
  },
  addOptionLabel: {
    type: String,
    default: 'Add a new category',
  },
});

const emit = defineEmits(['select-option:change', 'select-option:add-new']);
const model = defineModel();

const selectOptionElRef = ref();
const searchInputRef = ref();
const dropdownRef = ref();

const dropdownId = ref(`dropdown-${Date.now()}`);
const isOpen = ref(false);
const inputModel = ref("");
const radioModel = ref();
const searchQuery = ref('');
const isConfirmed = ref(!!model.value);
const dropdownPosition = ref(DROPDOWN_POSITIONS.BOTTOM); // top, bottom
const focusedOptionIndex = ref(-1);
const focusedOptionId = ref('');

const dropdownStyles = reactive({
  zIndex: '1000',
  position: 'fixed',
  top: '0px',
  left: '0px',
  width: '0px',
  margin: '0px',
});

const onClickOutsideHandler = [() => closeDropdown(), { ignore: [selectOptionElRef] }];

const isInputReadonly = computed(() => {
  if (!props.hasConfirm) {
    return false;
  }

  return !!(inputModel.value && isConfirmed.value);
});

const filteredOptions = computed(() => props.options.filter(
  (option) => option.name?.toLowerCase()?.includes(searchQuery.value?.toLowerCase())
));

// Compute all focusable options (including add option if present)
const focusableOptions = computed(() => {
  const options = [];
  if (props.hasAddOption) {
    options.push({ uuid: 'add-option', name: props.addOptionLabel, isAddOption: true });
  }
  options.push(...filteredOptions.value);
  return options;
});

// Get the currently focused option
const focusedOption = computed(() => {
  if (focusedOptionIndex.value >= 0 && focusedOptionIndex.value < focusableOptions.value.length) {
    return focusableOptions.value[focusedOptionIndex.value];
  }
  return null;
});

const setDropdownPosition = () => {
  const triggerEl = selectOptionElRef.value;
  const dropdownEl = dropdownRef.value;
  if (!triggerEl || !dropdownEl) return;

  const rect = triggerEl.getBoundingClientRect();
  const spaceBelow = window.innerHeight - rect.bottom;
  const spaceAbove = rect.top;

  // Decide dropdown placement
  const position =
    (spaceBelow >= MAX_DROPDOWN_HEIGHT || spaceBelow >= spaceAbove)
      ? DROPDOWN_POSITIONS.BOTTOM
      : DROPDOWN_POSITIONS.TOP;

  dropdownPosition.value = position;

  // Apply styles
  const top = position === DROPDOWN_POSITIONS.BOTTOM
    ? rect.bottom + 5
    : rect.top - dropdownEl.offsetHeight - 5;

  dropdownStyles.top = `${Math.max(top, 0)}px`;
  dropdownStyles.left = `${rect.left}px`;
  dropdownStyles.width = `${rect.width}px`;
  dropdownStyles.margin = (position === DROPDOWN_POSITIONS.TOP) ? '0 0 5px' : '5px 0 0';

  // Ensure dropdown stays within viewport
  nextTick(() => {
    ensureDropdownVisibility();
  });
};

const focusSearchInput = () => props.hasSearch && isInputReadonly.value && nextTick(() => searchInputRef.value?.focus());

// Keyboard navigation functions
const resetFocus = () => {
  focusedOptionIndex.value = -1;
  focusedOptionId.value = '';
};

const setFocusToSelectedOption = () => {
  if (radioModel.value?.uuid) {
    const selectedIndex = focusableOptions.value.findIndex(option => option.uuid === radioModel.value.uuid);
    if (selectedIndex >= 0) {
      focusedOptionIndex.value = selectedIndex;
      focusedOptionId.value = radioModel.value.uuid;
    }
  }
};

const updateFocusedOption = (newIndex) => {
  if (focusableOptions.value.length === 0) return;

  focusedOptionIndex.value = newIndex;
  const focusedOpt = focusableOptions.value[focusedOptionIndex.value];
  focusedOptionId.value = focusedOpt.uuid;
  scrollOptionIntoView(focusedOpt.uuid);
};

const moveFocusDown = () => {
  if (focusableOptions.value.length === 0) return;
  const newIndex = (focusedOptionIndex.value + 1) % focusableOptions.value.length;
  updateFocusedOption(newIndex);
};

const moveFocusUp = () => {
  if (focusableOptions.value.length === 0) return;
  const newIndex = focusedOptionIndex.value <= 0
    ? focusableOptions.value.length - 1
    : focusedOptionIndex.value - 1;
  updateFocusedOption(newIndex);
};

const selectFocusedOption = () => {
  const focused = focusedOption.value;
  if (!focused) return;

  if (focused.isAddOption) {
    addNewOption();
  } else {
    radioModel.value = focused;
    if (!props.hasConfirm) {
      selectOption();
    }
  }

  // Blur any active element to prevent unwanted re-triggering
  if (document.activeElement && document.activeElement.blur) {
    document.activeElement.blur();
  }
};

const scrollOptionIntoView = (optionUuid) => {
  nextTick(() => {
    const optionElement = document.querySelector(`[data-option-uuid="${optionUuid}"]`);
    if (optionElement && dropdownRef.value) {
      const dropdownList = dropdownRef.value.querySelector('.select-option-dropdown-list');
      if (dropdownList) {
        const optionRect = optionElement.getBoundingClientRect();
        const listRect = dropdownList.getBoundingClientRect();

        // Check if option is outside visible area
        if (optionRect.top < listRect.top || optionRect.bottom > listRect.bottom) {
          optionElement.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest',
            inline: 'nearest'
          });
        }
      }
    }
  });
};

// Enhanced dropdown positioning to ensure better visibility
const ensureDropdownVisibility = () => {
  if (!dropdownRef.value) return;

  const dropdownRect = dropdownRef.value.getBoundingClientRect();
  const { innerHeight: viewportHeight, innerWidth: viewportWidth } = window;

  let needsUpdate = false;
  let newTop = parseInt(dropdownStyles.top);
  let newLeft = parseInt(dropdownStyles.left);

  // Adjust if dropdown goes outside viewport
  if (dropdownRect.bottom > viewportHeight) {
    const overflow = dropdownRect.bottom - viewportHeight;
    newTop = Math.max(0, newTop - overflow - 10);
    needsUpdate = true;
  }

  if (dropdownRect.right > viewportWidth) {
    const overflow = dropdownRect.right - viewportWidth;
    newLeft = Math.max(0, newLeft - overflow - 10);
    needsUpdate = true;
  }

  // Update styles only if needed
  if (needsUpdate) {
    dropdownStyles.top = `${newTop}px`;
    dropdownStyles.left = `${newLeft}px`;
  }
};

// Keyboard event handler
const handleKeydown = (event) => {
  if (!isOpen.value) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      moveFocusDown();
      break;
    case 'ArrowUp':
      event.preventDefault();
      moveFocusUp();
      break;
    case 'Enter':
      event.preventDefault();
      selectFocusedOption();
      break;
    case 'Escape':
      event.preventDefault();
      closeDropdown();
      break;
  }
};

const setOpen = (val) => isOpen.value = val;
const openDropdown = async () => {
  setOpen(true);
  await nextTick();
  setDropdownPosition();
  focusSearchInput();

  // Set focus to selected option or first option
  if (radioModel.value?.uuid) {
    setFocusToSelectedOption();
  } else if (focusableOptions.value.length > 0) {
    focusedOptionIndex.value = 0;
    focusedOptionId.value = focusableOptions.value[0].uuid;
  }

  // Scroll selected option into view
  if (radioModel.value?.uuid) {
    scrollOptionIntoView(radioModel.value.uuid);
  }
};

const closeDropdown = () => {
  setOpen(false);
  clearSearch();
  resetFocus();
};

const toggleDropdown = () => !isOpen.value ? openDropdown() : closeDropdown();

const handleSearch = (event) => {
  searchQuery.value = event.target.value;

  // Reset focus to first option when searching
  if (isOpen.value && focusableOptions.value.length > 0) {
    focusedOptionIndex.value = 0;
    focusedOptionId.value = focusableOptions.value[0].uuid;
  }
};

const clearSearch = () => {
  searchQuery.value = "";
  resetFocus();
};

const selectOption = () => {
  if (radioModel.value?.['uuid']) {
    model.value = radioModel.value?.["uuid"];
    inputModel.value = radioModel.value?.['name'];
    emit("select-option:change", radioModel.value);
    isConfirmed.value = true;

    // Blur any active element to prevent unwanted re-triggering
    if (document.activeElement && document.activeElement.blur) {
      document.activeElement.blur();
    }

    closeDropdown();

    // Return focus to the main input after a short delay
    nextTick(() => {
      setTimeout(() => {
        const mainInput = selectOptionElRef.value?.querySelector('input[type="text"]');
        if (mainInput) {
          mainInput.focus();
        }
      }, 100);
    });
  }
};

const addNewOption = () => {
  emit('select-option:add-new', true);

  // Blur any active element to prevent unwanted re-triggering
  if (document.activeElement && document.activeElement.blur) {
    document.activeElement.blur();
  }

  closeDropdown();
};

watch(() => [model, props.options], (data) => {
  const [modelVal, optionsVal] = data;
  const item = optionsVal?.find((item) => item.uuid === modelVal.value);
  inputModel.value = item?.name || "";
  radioModel.value = item;
}, { immediate: true });

watch(radioModel, () => {
  if (!props.hasConfirm) {
    selectOption();
  }
});

// Watch for changes in filtered options to maintain focus
watch(filteredOptions, () => {
  if (isOpen.value && focusedOptionIndex.value >= 0) {
    // Ensure focused index is still valid
    if (focusedOptionIndex.value >= focusableOptions.value.length) {
      focusedOptionIndex.value = Math.max(0, focusableOptions.value.length - 1);
    }

    // Update focused option ID
    if (focusableOptions.value[focusedOptionIndex.value]) {
      focusedOptionId.value = focusableOptions.value[focusedOptionIndex.value].uuid;
    }
  }
});

// Watch for focus changes to scroll into view
watch(focusedOptionId, (newId) => {
  if (newId && isOpen.value) {
    scrollOptionIntoView(newId);
  }
});


// Optimized event listeners with debouncing for performance
const debouncedSetDropdownPosition = debounce(setDropdownPosition, 100);

// Event listeners
useEventListener('resize', debouncedSetDropdownPosition);
useEventListener('scroll', debouncedSetDropdownPosition);
useEventListener('keydown', handleKeydown);

// Debounce function for performance optimization
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

onMounted(() => {
  setDropdownPosition();
});
</script>
